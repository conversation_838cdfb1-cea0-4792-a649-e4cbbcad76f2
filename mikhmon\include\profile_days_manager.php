<?php
/*
 *  Profile Days Manager for Mikhmon
 *  Manages profile days information
 */

if(isset($_SERVER["REQUEST_URI"]) && substr($_SERVER["REQUEST_URI"], -25) == "profile_days_manager.php"){
    header("Location:./");
}

class ProfileDaysManager {
    private $dataFile;
    
    public function __construct() {
        $this->dataFile = __DIR__ . '/profile_days.json';
        if (!file_exists($this->dataFile)) {
            file_put_contents($this->dataFile, '{}');
        }
    }
    
    /**
     * حفظ عدد الأيام للباقة
     */
    public function saveProfileDays($profileName, $days) {
        $data = $this->loadData();
        $data[$profileName] = intval($days);
        return $this->saveData($data);
    }
    
    /**
     * الحصول على عدد الأيام للباقة
     */
    public function getProfileDays($profileName) {
        $data = $this->loadData();
        return isset($data[$profileName]) ? $data[$profileName] : 0;
    }
    
    /**
     * حذف معلومات الباقة
     */
    public function deleteProfile($profileName) {
        $data = $this->loadData();
        if (isset($data[$profileName])) {
            unset($data[$profileName]);
            return $this->saveData($data);
        }
        return true;
    }
    
    /**
     * الحصول على جميع الباقات مع عدد الأيام
     */
    public function getAllProfiles() {
        return $this->loadData();
    }
    
    /**
     * تحديث اسم الباقة
     */
    public function updateProfileName($oldName, $newName) {
        $data = $this->loadData();
        if (isset($data[$oldName])) {
            $data[$newName] = $data[$oldName];
            unset($data[$oldName]);
            return $this->saveData($data);
        }
        return true;
    }
    
    /**
     * تحميل البيانات من الملف
     */
    private function loadData() {
        $content = file_get_contents($this->dataFile);
        $data = json_decode($content, true);
        return $data ? $data : array();
    }
    
    /**
     * حفظ البيانات في الملف
     */
    private function saveData($data) {
        $json = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        return file_put_contents($this->dataFile, $json) !== false;
    }

    /**
     * تحويل عدد الأيام إلى تنسيق MikroTik interval
     */
    public function daysToInterval($days) {
        return $days . 'd';
    }

    /**
     * تحويل MikroTik interval إلى عدد الأيام
     */
    public function intervalToDays($interval) {
        if (preg_match('/(\d+)d/', $interval, $matches)) {
            return intval($matches[1]);
        }
        return 0;
    }

    /**
     * حساب تاريخ الانتهاء بناءً على تاريخ البداية وعدد الأيام
     */
    public function calculateExpiryDate($startDate, $days) {
        try {
            // تحويل التاريخ من تنسيق MikroTik إلى تنسيق قابل للقراءة
            $date = DateTime::createFromFormat('M/d/Y', $startDate);
            if ($date === false) {
                $date = DateTime::createFromFormat('d/m/Y', $startDate);
            }
            if ($date === false) {
                $date = new DateTime(); // استخدم التاريخ الحالي كبديل
            }
            $date->add(new DateInterval('P' . $days . 'D'));
            return $date->format('M/d/Y');
        } catch (Exception $e) {
            return "";
        }
    }

    /**
     * حساب عدد الأيام المتبقية
     */
    public function calculateRemainingDays($expiryDate) {
        try {
            $expiry = DateTime::createFromFormat('M/d/Y', $expiryDate);
            $today = new DateTime();

            if (!$expiry) {
                return 0;
            }

            $diff = $today->diff($expiry);

            if ($expiry < $today) {
                return 0; // منتهي
            }

            return $diff->days;
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * تنسيق التاريخ للعرض
     */
    public function formatDateForDisplay($date) {
        try {
            $dateObj = DateTime::createFromFormat('M/d/Y', $date);
            if ($dateObj) {
                return $dateObj->format('d/m/Y');
            }
            return $date;
        } catch (Exception $e) {
            return $date;
        }
    }
}







?>
